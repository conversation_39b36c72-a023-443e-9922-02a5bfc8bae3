<?php

namespace App\Services;

use App\DivisionType;
use App\Line;
use App\LineDivision;
use App\Models\BranchSale;
use App\SalesSetting;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BranchSalesDetailsService
{
    private Carbon $from;
    private Carbon $to;
    private bool $isUser = false;
    private bool $isDiv = false;

    public function fetch($saleFilter): array
    {
        $sale = $saleFilter;
        /**@var User  */
        $user = Auth::user();
        $this->from = Carbon::parse($sale['fromDate'])->startOfDay();
        $this->to = Carbon::parse($sale['toDate'])->endOfDay();
        $perDivOrUserFilter = $sale['filter'];
        $this->isDiv = $perDivOrUserFilter === 1;
        $this->isUser = $perDivOrUserFilter === 2;
        $fields = $this->getFields();
        $lines = Line::whereIn("id", $sale['lines'])
            ->with([
                "distributors" => fn($q) => $q->when($sale['distributors'], fn($q) => $q->whereIn("distributors.id", $sale['distributors'])),
                "allProducts" => fn($q) => $q->when($sale['products'], fn($q) => $q->whereIn("line_products.product_id", $sale['products'])),
                "divisions" => fn($q) => $q->when($sale['divisions'], fn($q) => $q->whereIn("line_divisions.id", $sale['divisions'])),
                "users" => fn($q) => $q->when($sale['users'], fn($q) => $q->whereIn("line_users.user_id", $sale['users']))
            ])->get();

        $data = $lines->flatMap(
            fn(Line $line) => $this->buildSalesQuery($user, $line)->get()
        );

        $uniqueData = $data->unique(fn($item) => $this->getUniqueKey($item, $perDivOrUserFilter))->values();
        return [$uniqueData, $fields];
    }


    private function buildSalesQuery(User $user, Line $line)
    {
        $divisions = $user->allAboveDivisions($line)->pluck('id');

        $product_avg_price = 'COALESCE(
                        (
                                SELECT avg_price
                                FROM crm_product_prices
                                WHERE crm_product_prices.product_id = crm_branch_sales.product_id
                                AND crm_product_prices.deleted_at IS NULL
                                AND crm_product_prices.distributor_id = crm_branch_sales.distributor_id
                                AND crm_product_prices.from_date <= crm_branch_sales.date
                                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_branch_sales.date)
                                LIMIT 1
                            ),
                            (
                                    SELECT avg_price
                                    FROM crm_product_prices
                                    WHERE crm_product_prices.product_id = crm_branch_sales.product_id
                                    AND crm_product_prices.deleted_at IS NULL
                                    AND crm_product_prices.distributor_id IS NULL
                                    AND crm_product_prices.from_date <= crm_branch_sales.date
                                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_branch_sales.date)
                                    LIMIT 1
                            )
                        )';
        $query = BranchSale::select([
            "branch_sales.id",
            'lines.name as line',
            'line_divisions.name as division',
            'branch_sales.div_id as div_id',
            DB::raw('DATE_FORMAT(crm_branch_sales.date,"%Y-%m-%d") as date'),
            DB::raw('IFNULL(crm_distributors.name,"") AS distributor'),
            'branch_mappings.distributor_id',
            'branch_mappings.name as name',
            'branch_sales.product_id',
            'products.name as product',
            'division_types.color as color',
            DB::raw('FORMAT((crm_branch_sales.quantity),2) as units'),
            DB::raw("FORMAT($product_avg_price,2) as pro_price"),
            DB::raw('crm_branch_sales.bonus as bonus'),
            DB::raw("
                   CASE
                     WHEN crm_branch_sales.value = 0 THEN FORMAT(($product_avg_price * crm_branch_sales.quantity),2)
                     WHEN crm_branch_sales.value > 0 THEN FORMAT(crm_branch_sales.value,2)
                   END as value
                   "),
        ])
            ->join(
                'products',
                fn($join) => $join->on('branch_sales.product_id', 'products.id')
                    ->whereIn('branch_sales.product_id', $line->allProducts->pluck("id")->toArray())
            )
            ->join('branch_mappings', 'branch_mappings.id', 'branch_sales.branch_mapping_id')
            ->join('distributors', 'distributors.id', 'branch_sales.distributor_id')
            ->join('line_divisions', fn($join) => $join->on('branch_sales.div_id', 'line_divisions.id'))
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('lines', 'line_divisions.line_id', 'lines.id');

        if ($this->isUser) {
            $query->addSelect(DB::raw('IFNULL(crm_mr.fullname,"") AS employee'));
            $query->addSelect(DB::raw('IFNULL(crm_mr.emp_code,"") AS emp_code'));

            $query->leftjoin(
                'line_users_divisions as low_level',
                fn($join) => $join->on('line_divisions.id', 'low_level.line_division_id')
                    ->where('low_level.from_date', '<=', Carbon::now())
                    ->whereNull('low_level.deleted_at')
                    ->where(fn($q) => $q->whereNull('low_level.to_date')
                        ->orWhere('low_level.to_date', '>=', Carbon::now()))
            )
                ->leftJoin(
                    'users as mr',
                    fn($join) => $join->on('low_level.user_id', 'mr.id')
                        ->whereIn("mr.id", $line->users->pluck("id")->toArray())
                );
        }

        if ($user->hasRole('admin')) {
            $query->whereIn('branch_sales.div_id', $line->divisions->pluck("id")->toArray());
        } else {
            $query->whereIn('branch_sales.div_id', $divisions);
        }

        return $query
            ->whereIn('branch_sales.distributor_id', $line->distributors->pluck("id")->toArray())
            ->whereBetween('branch_sales.date', [$this->from->toDateString(), $this->to->toDateString()]);
    }


    private function getFields(): array
    {
        $fields = ["line"];
        if ($this->isDiv) {
            array_unshift($fields, "division");
        }
        if ($this->isUser) {
            $fields[] = "employee";
            $fields[] = "emp_code";
        }
        return [
            ...$fields,
            "product",
            "distributor",
            'name',
            "pro_price",
            "units",
            "bonus",
            "value",
            "date",
        ];
    }


    private function getUniqueKey($item, $perDivOrUserFilter): array
    {
        if ($perDivOrUserFilter == 1) {
            return [$item['id'], $item['line'], $item['division'], $item['product'], $item['distributor']];
        }
        return [$item['id'], $item['line'], $item['employee'], $item['product'], $item['distributor']];
    }
}
