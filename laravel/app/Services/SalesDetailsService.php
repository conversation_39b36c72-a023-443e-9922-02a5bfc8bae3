<?php

namespace App\Services;

use App\DivisionType;
use App\Line;
use App\SalesSetting;
use App\SalesTypes;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDetailsFieldType;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesDetailsService
{
    private User $authUser;
    private array $saleFilter;

    private bool $isAchievement = false;
    private Carbon $from;
    private Carbon $to;
    private CarbonPeriod $period;

    private bool $isPerDiv = true;

    private const FIELD_TYPES = [
        "line" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "manager" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "employee" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "emp_code" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "product" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "distributor" => [SaleDetailsFieldType::DETAILS],
        "distribution_type" => [SaleDetailsFieldType::DETAILS],
        "type" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "name" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "brick" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "address" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "pro_price" => [SaleDetailsFieldType::DETAILS],
        "target_units" => [SaleDetailsFieldType::ACHIEVEMENT],
        "units" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "bonus" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "target_value" => [SaleDetailsFieldType::ACHIEVEMENT],
        "value" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "date" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "ratio" => [SaleDetailsFieldType::DETAILS]
    ];

    public function generateKey(): string
    {
        return 'filter_data:' .
            '_auth_user:' . $this->authUser->id .
            '_lines:' . implode(',', $this->saleFilter['lines']) .
            '_report_type:' . $this->saleFilter['report_type'] .
            '_users:' . implode(',', $this->saleFilter['users']) .
            '_divisions:' . implode(',', $this->saleFilter['divisions']) .
            '_filter:' . $this->saleFilter['filter'] .
            '_distributors:' . implode(',', $this->saleFilter['distributors']) .
            '_products:' . implode(',', $this->saleFilter['products']) .
            '_type:' . $this->saleFilter['type'] .
            '_fromDate:' . $this->saleFilter['fromDate'] .
            '_toDate:' . $this->saleFilter['toDate'];
    }

    public function initializeDates(): void
    {
        $this->from = Carbon::parse($this->saleFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->saleFilter['toDate'])->endOfDay();
        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);
    }

    public function initFilters(): void
    {
        $this->isPerDiv = $this->saleFilter['filter'] == 1;
        $this->isAchievement = $this->saleFilter['report_type'] == 2;

        Log::info("$this->isAchievement ? 'achievement' : 'detail'");
    }

    public function fetch(User $user, $saleFilter): array
    {
        $this->authUser = $user;
        $this->saleFilter = $saleFilter;
        $this->initFilters();
        $this->initializeDates();

        $key = $this->generateKey();
        [$uniqueData, $fields] = Cache::remember(
            $key,
            now()->addHours(2),
            function () use ($saleFilter) {
                $fields = $this->getFields();
                $types = SalesTypes::when(!is_null($saleFilter['type']), fn($q) => $q->where("id", $saleFilter['type']))->pluck('id');
                $division_type = DivisionType::where('last_level', 1)->value('id');
                $salesPerDistributor = SalesSetting::where('key', 'mapping_with_distributor')->value('value') === 'Yes';
                $lines = Line::whereIn("id", $saleFilter['lines'])
                    ->with([
                        "distributors" => fn($q) => $q->when($saleFilter['distributors'], fn($q) => $q->whereIn("distributors.id", $saleFilter['distributors'])),
                        "allProducts" => fn($q) => $q->whereIn("line_products.product_id", $saleFilter['products']),
                        "divisions" => fn($q) => $q->when($saleFilter['divisions'], fn($q) => $q->whereIn("line_divisions.id", $saleFilter['divisions'])),
                        "users" => fn($q) => $q->when($saleFilter['users'], fn($q) => $q->whereIn("line_users.user_id", $saleFilter['users']))
                    ])->get();

                $filtered = $lines->flatMap(
                    fn(Line $line) => $this->filterLine($line)
                );
                $belowDivisions = $filtered->flatMap(
                    fn($userOrDiv) => $this->getBelowDivisions($userOrDiv["object"], $userOrDiv["line"], $division_type)
                );

                $productIds = $lines
                    ->pluck("allProducts")
                    ->collapse()
                    ->unique('id')
                    ->pluck("id")
                    ->toArray();

                $distributors = $lines->pluck("distributors")
                    ->collapse()
                    ->unique('id')
                    ->pluck('id')
                    ->toArray();

                $details = $this->details($lines, $distributors, $productIds, $belowDivisions->toArray(), $types, $salesPerDistributor);

                $uniqueData = $details->unique(fn($item) => $this->getUniqueKey($item))->values();

                return [$uniqueData, $fields];
            }
        );

        return [$uniqueData, $fields];
    }

    private function filterLine(Line $line): Collection
    {

        if ($this->isPerDiv)
            return $this->authUser->filterDivisions(
                $line,
                $line->divisions($this->from, $this->to)
                    ->when(!empty($this->saleFilter['divisions']), fn($q) => $q->whereIn("id", $this->saleFilter['divisions']))
                    ->get(),
                $this->saleFilter,
                $this->from,
                $this->to
            )->map(fn($object) => ['object' => $object, 'line' => $line]);

        return $this->authUser->filterUsers(
            $line,
            $line->users($this->from, $this->to)
                ->when(!empty($this->saleFilter['users']), fn($q) => $q->whereIn("id", $this->saleFilter['users']))
                ->get(),
            $this->saleFilter,
            $this->from,
            $this->to
        )->map(fn($object) => ['object' => $object, 'line' => $line]);
    }

    private function details(
        Collection $lines,
        array      $distributors,
        array      $productIds,
        array      $belowDivisions,
        Collection $types,
        bool       $isSalesPerDistributor
    ): Collection
    {
        return $this->getSalesData(
            $lines,
            $productIds,
            $distributors,
            $belowDivisions,
            $types,
            $isSalesPerDistributor
        )->orderBySequence('crm_sales_details.div_id', $belowDivisions)
            ->get();
    }

    private function getBelowDivisions($object, $line, $division_type)
    {
        if ($this->isPerDiv) {
            return $object->getBelowDivisions()->where('division_type_id', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        }
        return $object->allBelowDivisions($line)->where('division_type_id', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
    }

    private function getSalesData(
        Collection $lines,
        array      $productIds,
        array      $distributors,
        array      $belowDivisions,
        Collection $types,
        bool       $isSalesPerDistributor
    )
    {
        $dates = [];
        foreach ($this->period as $date) {
            $dates[] = $date;
        }
        return $this->buildSalesQuery($lines, $productIds, $distributors, $belowDivisions, $dates, $types, $isSalesPerDistributor);
    }


    private function getSelectionForAchievement(): array
    {
        $product_avg_price = $this->getOptimizedProductPrice();
        return [
            'lines.name as line',
            'line_divisions.name as division',
            'sales_details.div_id',
            DB::raw('MAX(sales.distributor_id) as distributor_id'),
            DB::raw('IFNULL(bricks.name, "") as brick'),
            DB::raw('IFNULL(sales_details.brick_id, "") as brick_id'),
            DB::raw('DATE_FORMAT(sales_details.date, "%Y-%m-%d") as date'),
            DB::raw('MAX(IFNULL(higher.fullname, "")) as manager'),

            // Optimized employee lookup using CTE
            DB::raw("MAX(IFNULL(udl.fullname, '')) as employee"),
            DB::raw("MAX(IFNULL(udl.emp_code, '')) as emp_code"),

            DB::raw('MAX(sales_types.name) as type'),
            DB::raw('MAX(mappings.name) as name'),
            DB::raw('IFNULL(MAX(mappings.address), "") as address'),
            'sales.product_id',
            'products.name as product',
            DB::raw('MAX(division_types.color) as color'),
            DB::raw("FORMAT(MAX($product_avg_price),2) as pro_price"),
            DB::raw('FORMAT(SUM(sales_details.quantity), 2) as units'),

            DB::raw(
                '(SELECT IFNULL(SUM(target_details.target),"")
                FROM crm_target_details target_details
                WHERE target_details.product_id = sales.product_id
                AND target_details.div_id = sales_details.div_id
                AND target_details.brick_id = sales_details.brick_id
                AND target_details.deleted_at IS NULL
                AND target_details.date = sales_details.date
                ) as target_units'
            ),
            DB::raw('SUM(sales_details.bonus) as bonus'),
            DB::raw("
                    FORMAT(SUM(
                        CASE
                            WHEN sales.value > 0 THEN sales_details.value
                            ELSE $product_avg_price * sales_details.quantity
                        END
                       )
                    , 2) as value
                   "
            ),
            DB::raw("
               FORMAT(AVG(
                   CASE
                        WHEN target_details.value > 0 THEN target_details.value
                        ELSE $product_avg_price * target_details.target
                   END
               ),2) as target_value
               "
            )
        ];
    }

    public function getSelectionsForDetails(): array
    {
        $product_avg_price = $this->getOptimizedProductPrice();

        return [
            DB::raw('distinct sales_details.id as detail_id'),
            'sales.id as id',
            'lines.name as line',
            'line_divisions.name as division',
            'sales_details.div_id as div_id',
            DB::raw('IFNULL(bricks.name,"") AS brick'),
            DB::raw('IFNULL(sales_details.brick_id,"") AS brick_id'),
            DB::raw('DATE_FORMAT(sales_details.date,"%Y-%m-%d") as date'),
            DB::raw('IFNULL(higher.fullname,"") AS manager'),

            // Optimized employee lookup using CTE
            DB::raw('IFNULL(udl.fullname,"") as employee'),
            DB::raw('IFNULL(udl.emp_code,"") as emp_code'),

            DB::raw('IFNULL(distributors.name,"") AS distributor'),
            'mappings.distributor_id',
            'sales_types.name as type',
            'mappings.name as name',
            'mappings.id as mapping_id',
            'mappings.code as mapping_code',
            DB::raw(
                "
                   CASE
                     WHEN sales.ceiling = '0' THEN 'NORMAL'
                     WHEN sales.ceiling = '2' THEN 'DISTRIBUTED'
                   END as distribution_type"
            ),
            DB::raw('IFNULL(mappings.address,"") AS address'),
            'sales.product_id',
            'products.name as product',
            'division_types.color as color',
            DB::raw('FORMAT((sales_details.quantity),2) as units'),
            DB::raw("FORMAT($product_avg_price,2) as pro_price"),
            DB::raw('sales_details.bonus as bonus'),
            DB::raw("
                   CASE
                       WHEN sales.value = 0 THEN FORMAT(($product_avg_price * sales_details.quantity),2)
                       ELSE FORMAT(sales_details.value,2)
                   END as value
                   "
            ),
            'sales_details.ratio'
        ];

    }

    private function buildSalesQuery(
        Collection $lines,
        array      $productIds,
        array      $distributorIds,
        array      $belowDivisions,
        array      $dates,
        Collection $types,
        bool       $isSalesPerDistributor
    )
    {
        $selections = $this->isAchievement ? $this->getSelectionForAchievement()
            : $this->getSelectionsForDetails();

        // Create the optimized CTEs
        $cteQuery = $this->createOptimizedCTEs();
        $fromDate = $this->from->toDateString();
        $toDate = $this->to->toDateString();

        // Convert selections array to string for raw SQL
        $selectionsString = implode(', ', array_map(function($selection) {
            return is_string($selection) ? $selection : $selection->getValue(DB::connection()->getQueryGrammar());
        }, $selections));

        // Build the complete query with CTEs
        $rawQuery = $cteQuery . " SELECT " . $selectionsString . "
            FROM crm_sales as sales

            -- Products join
            INNER JOIN crm_products as products
                ON sales.product_id = products.id

            -- Sales details join
            INNER JOIN crm_sales_details as sales_details
                ON sales.id = sales_details.sale_id

            -- Mapping joins
            LEFT JOIN crm_mapping_sale as mapping_sale ON sales.id = mapping_sale.sale_id
            LEFT JOIN crm_target_details as target_details
                ON sales.product_id = target_details.product_id
                AND sales_details.div_id = target_details.div_id
                AND sales_details.brick_id = target_details.brick_id
                AND sales_details.date = target_details.date

            INNER JOIN crm_mappings as mappings
                ON mapping_sale.mapping_id = mappings.id
            ";

        // Add distributor join based on condition
        if ($isSalesPerDistributor && !$this->isAchievement) {
            $rawQuery .= "
            LEFT JOIN crm_distributors as distributors ON mappings.distributor_id = distributors.id";
        } elseif (!$this->isAchievement) {
            $rawQuery .= "
            LEFT JOIN crm_distributors as distributors ON sales.distributor_id = distributors.id";
        }

        $rawQuery .= "
            -- Other joins
            LEFT JOIN crm_sales_types as sales_types ON mappings.mapping_type_id = sales_types.id
            LEFT JOIN crm_bricks as bricks ON sales_details.brick_id = bricks.id
            LEFT JOIN crm_line_divisions as line_divisions ON sales_details.div_id = line_divisions.id
            LEFT JOIN crm_division_types as division_types ON line_divisions.division_type_id = division_types.id

            -- Optimized user lookup via CTE
            LEFT JOIN user_division_lookup as udl ON line_divisions.id = udl.line_division_id

            -- Optimized product price lookup via CTEs
            LEFT JOIN product_price_lookup as ppl_specific
                ON sales.product_id = ppl_specific.product_id
                AND sales.distributor_id = ppl_specific.distributor_id
            LEFT JOIN product_price_lookup as ppl_general
                ON sales.product_id = ppl_general.product_id
                AND ppl_general.distributor_id IS NULL

            -- Existing joins for hierarchy
            LEFT JOIN crm_line_users_divisions as low_level
                ON line_divisions.id = low_level.line_division_id
                AND low_level.deleted_at IS NULL
                AND (
                    (low_level.to_date IS NULL OR low_level.to_date BETWEEN '$fromDate' AND '$toDate' OR low_level.to_date >= '$toDate')
                    AND
                    (low_level.from_date <= '$fromDate' OR low_level.from_date BETWEEN '$fromDate' AND '$toDate')
                )
            LEFT JOIN crm_users as mr ON low_level.user_id = mr.id

            LEFT JOIN crm_line_div_parents as line_div_parents
                ON line_divisions.id = line_div_parents.line_div_id
                AND line_div_parents.deleted_at IS NULL
                AND (
                    (line_div_parents.to_date IS NULL OR line_div_parents.to_date BETWEEN '$fromDate' AND '$toDate' OR line_div_parents.to_date >= '$toDate')
                    AND
                    (line_div_parents.from_date <= '$fromDate' OR line_div_parents.from_date BETWEEN '$fromDate' AND '$toDate')
                )
            LEFT JOIN crm_line_divisions as parent ON line_div_parents.parent_id = parent.id

            LEFT JOIN crm_line_users_divisions as high_level
                ON line_div_parents.parent_id = high_level.line_division_id
                AND (
                    (high_level.to_date IS NULL OR high_level.to_date BETWEEN '$fromDate' AND '$toDate' OR high_level.to_date >= '$toDate')
                    AND
                    (high_level.from_date <= '$fromDate' OR high_level.from_date BETWEEN '$fromDate' AND '$toDate')
                )
            LEFT JOIN crm_users as higher ON high_level.user_id = higher.id
            LEFT JOIN crm_lines as lines ON line_divisions.line_id = lines.id

            -- WHERE clause
            WHERE sales.ceiling IN (" . Ceiling::DISTRIBUTED->value . ", " . Ceiling::BELOW->value . ")
                AND sales.product_id IN (" . implode(',', $productIds) . ")
                AND sales_details.div_id IN (" . implode(',', $belowDivisions) . ")
                AND sales_details.date IN ('" . implode("','", array_map(function($date) {
                    return $date->toDateString();
                }, $dates)) . "')
                AND mappings.mapping_type_id IN (" . implode(',', $types->toArray()) . ")
                AND (mappings.line_id IN (" . implode(',', $lines->pluck('id')->toArray()) . ") OR mappings.line_id IS NULL)
                AND (mappings.distributor_id IN (" . implode(',', $distributorIds) . ") OR mappings.distributor_id IS NULL)
            ";

        // Add GROUP BY for achievement reports
        if ($this->isAchievement) {
            $rawQuery .= "
            GROUP BY
                sales_details.brick_id,
                bricks.name,
                sales_details.div_id,
                line_divisions.name,
                lines.id,
                lines.name,
                sales_details.date,
                sales.product_id,
                products.name";
        }

        // Prepare bindings for the CTEs
        $bindings = [
            // User lookup CTE bindings
            $fromDate, $fromDate, $fromDate, $fromDate, $toDate, $fromDate,
            // Price lookup CTE bindings
            $fromDate, $fromDate, $fromDate, $fromDate, $toDate, $fromDate
        ];

        // Execute the raw query with bindings
        return collect(DB::select($rawQuery, $bindings));
    }

    /**
     * Create CTEs for optimized data lookup using window functions
     */
    private function createOptimizedCTEs(): string
    {
        return "
            WITH ranked_user_divisions AS (
                SELECT
                    line_division_id,
                    user_id,
                    from_date,
                    to_date,
                    ROW_NUMBER() OVER (
                        PARTITION BY line_division_id, DATE(?)
                        ORDER BY
                            CASE
                                WHEN ? BETWEEN from_date AND COALESCE(to_date, '9999-12-31') THEN 0
                                ELSE 1
                            END,
                            from_date DESC
                    ) as rn
                FROM crm_line_users_divisions
                WHERE deleted_at IS NULL
                    AND (
                        (from_date <= ? AND (to_date IS NULL OR to_date >= ?))
                        OR
                        (from_date <= ? AND to_date >= ?)
                    )
            ),
            user_division_lookup AS (
                SELECT
                    rud.line_division_id,
                    u.fullname,
                    u.emp_code
                FROM ranked_user_divisions rud
                JOIN crm_users u ON rud.user_id = u.id
                WHERE rud.rn = 1 AND u.deleted_at IS NULL
            ),
            ranked_product_prices AS (
                SELECT
                    product_id,
                    distributor_id,
                    avg_price,
                    from_date,
                    to_date,
                    ROW_NUMBER() OVER (
                        PARTITION BY product_id, COALESCE(distributor_id, 0), DATE(?)
                        ORDER BY
                            CASE
                                WHEN distributor_id IS NOT NULL THEN 0
                                ELSE 1
                            END,
                            CASE
                                WHEN DATE(?) BETWEEN from_date AND COALESCE(to_date, '9999-12-31') THEN 0
                                ELSE 1
                            END,
                            from_date DESC
                    ) as rn
                FROM crm_product_prices
                WHERE deleted_at IS NULL
                    AND (
                        (from_date <= ? AND (to_date IS NULL OR to_date >= ?))
                        OR
                        (from_date <= ? AND to_date >= ?)
                    )
            ),
            product_price_lookup AS (
                SELECT
                    product_id,
                    distributor_id,
                    avg_price
                FROM ranked_product_prices
                WHERE rn = 1
            )
        ";
    }

    /**
     * Get optimized product price using CTE join instead of subquery
     */
    private function getOptimizedProductPrice(): string
    {
        return 'COALESCE(ppl_specific.avg_price, ppl_general.avg_price, 0)';
    }


    private function getFields(): array
    {
        $fields = array_map(fn($types) => SaleDetailsFieldType::combine(...$types), self::FIELD_TYPES);

        if ($this->isPerDiv) {
            $fields = array_merge(
                ["division" =>
                    SaleDetailsFieldType::combine(
                        SaleDetailsFieldType::DETAILS,
                        SaleDetailsFieldType::ACHIEVEMENT
                    )
                ],
                $fields
            );
        }

        $type = $this->isAchievement ? SaleDetailsFieldType::ACHIEVEMENT : SaleDetailsFieldType::DETAILS;

        return SaleDetailsFieldType::getItemsByType($fields, $type);
    }

    private function getUniqueKey($item): array
    {
        if ($this->isPerDiv) {
            return [$item['detail_id'], $item['line'], $item['division'], $item['product'], $item['distributor'], $item['type']];
        }
        return [$item['detail_id'], $item['line'], $item['employee'], $item['product'], $item['distributor'], $item['type']];
    }
}
