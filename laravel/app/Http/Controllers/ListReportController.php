<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\Action;
use App\ActualVisit;
use App\ActualVisitManager;
use App\Classes;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Line;
use App\LineDivision;
use App\Models\AccountLocationVerification;
use App\Models\ActualDoubleFeedback;
use App\Models\EDetailing\Statistic;
use App\Models\ListType;
use App\Models\NewAccountDoctor;
use App\Permission;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Services\DoctorService;
use App\Services\ListService;
use App\Speciality;
use App\VisitAttachment;
use App\VisitGiveaway;
use App\VisitProduct;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Predis\Configuration\Option\Exceptions;

class ListReportController extends ApiController
{
    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $specialities = Speciality::select('id', 'name')->get();
        $account_types = AccountType::select('id', 'name')->get();
        $classes = Classes::select('id', 'name')->get();
        return $this->respond(compact('lines', 'specialities', 'account_types', 'classes'));
    }
    public function getLineData(Line $line)
    {
        if ($line) {
            /**@var User $user */
            $user = Auth::user();
            $division_type = DivisionType::where('last_level', '=', 1)->value('id');
            $divisions = $user->userDivisions($line)->where('division_type_id', $division_type)->values();
            $users = $user->belowUsersWithPositions($line);
            $specialities = $line->specialities;
            $account_types = AccountType::select('id', 'name')->get();
            $classes = Classes::select('id', 'name')->get();
            return $this->respond(compact('users', 'divisions', 'specialities', 'account_types', 'classes'));
        } else {
            // throw new CrmException('this');
            return $this->respond();
        }
    }
    public function active($list, $divisions, $date)
    {
        $accounts = (new ListService())->getActiveList($date, $date, [$list['line']], $divisions, $list['bricks'], $list['specialities'], $list['types']);
        return $accounts;
    }
    public function accountLocation(Request $request)
    {
        $account = $request->account;
        $location = [
            'account_id' => $account['account_id'],
            'account' => $account['account'],
            'doctor' => $account['doctor'] ?? '',
            'lng' => (float)$account['lng'],
            'lat' => (float)$account['lat']
        ];
        return $this->respond($location);
    }

    public function masterList($list, $divisions)
    {
        $accounts = (new ListService())->getMasterList(
            [$list['line']],
            $divisions,
            $list['bricks'],
            $list['specialities'],
            $list['types']
        );
        return $accounts;
    }

    public function all($list, $divisions)
    {
        $accounts = (new ListService())->getAllList(
            null,
            null,
            [$list['line']],
            $divisions,
            $list['bricks'],
            $list['specialities'],
            $list['types']
        );
        return $accounts;
    }

    public function inActive($list, $divisions)
    {
        $accounts = Account::select(
            'accounts.id as id',
            'accounts.id as account_id',
            'lines.name as line',
            DB::raw('IFNULL(crm_lines.name,"") as line'),
            'lines.id as line_id',
            DB::raw('IFNULL(crm_line_divisions.name,"") as division'),
            DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
            'account_lines.ll as lat',
            'account_lines.lg as lng',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as emp'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_accounts.mobile,"") as acc_mobile'),
            DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
            DB::raw('IFNULL(crm_doctors.mobile,"") as doc_mobile'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            'specialities.name as speciality',
            DB::raw('IFNULL(group_concat(distinct DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y")),"") as from_date'),
            DB::raw('IFNULL(group_concat(distinct DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y")),"") as to_date'),
            'division_types.color'
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id')
            // ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('account_lines', function ($join) use ($list, $divisions) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->where('account_lines.line_id', $list['line'])
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions)
                    // ->where('account_lines.from_date', '<=', Carbon::now())
                    // ->where(fn ($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
                    ->where('account_lines.deleted_at', null);
            })
            // ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
            ->leftJoin('new_account_doctors', function ($join) use ($list) {
                $join->on('accounts.id', 'new_account_doctors.account_id')
                    ->where('new_account_doctors.line_id', $list['line'])
                    ->where(fn($q) => $q->where('new_account_doctors.from_date', '>', (string)Carbon::now())
                        ->orWhere('new_account_doctors.to_date', '<', (string)Carbon::now()))
                    ->where('new_account_doctors.deleted_at', null);
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin(
                'line_users_divisions',
                function ($join) {
                    $join->on('line_divisions.id', '=', 'line_users_divisions.line_division_id')
                        ->where('line_users_divisions.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                }
            )
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            // ->where('accounts.inactive_date', '<=', Carbon::now())
            // ->where('doctors.inactive_date', '<=', Carbon::now())
            ->where('account_lines.line_id', $list['line'])
            ->whereIn('account_lines.line_division_id', $divisions)
            ->where('new_account_doctors.line_id', $list['line'])
            ->whereIntegerInRaw('accounts.type_id', $list['types'])
            ->whereIntegerInRaw('speciality_id', $list['specialities'])
            ->where('accounts.deleted_at', null)
            ->where('doctors.deleted_at', null)
            ->orderBy('doctors.ucode', 'asc');
        if (!empty($list['bricks'])) {
            $accounts = $accounts->whereIntegerInRaw('bricks.id', $list['bricks']);
        }
        $accounts = $accounts->groupBy(
            "accounts.id",
            "doctors.id",
            "lines.id",
            "line_divisions.id",
            "a.name",
            "account_lines.ll",
            "account_lines.lg",
            // 'new_account_doctors.from_date',
            // 'new_account_doctors.to_date'
        )->get();
        return $accounts;
    }
    public function verifiedLocation($list, $divisions, $date, $isAdmin)
    {
        $accounts = (new ListService())->getActiveListLocations(
            $date,
            $date,
            [$list['line']],
            $divisions,
            $list['bricks'],
            $list['specialities'],
            $list['types'],
            $isAdmin
        );
        return $accounts;
    }
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $listFilter = $request->listFilter;
        $line = Line::find($listFilter['line']);
        $filter = $listFilter['filter'] == 1 ? 1 : 2;
        $filtered = new Collection();
        $result = new Collection();
        $date = Carbon::parse($listFilter['date'])->startOfMonth() ?? Carbon::now()->startOfMonth();
        $year = Carbon::parse($date)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($date, '1 month', $date);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        if ($filter == 1) {
            $divisions = $line->divisions()
                ->when(!empty($listFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $listFilter['divisions']))->get();
            $filtered = $authUser->filterDivisions($line, $divisions, $listFilter);
            $result = $filtered->where('division_type_id', '=', $division_type)->pluck('id')->unique()->toArray();
        }
        if ($filter == 2) {
            $users = $line->users()
                ->when(!empty($listFilter['users']), fn($q) => $q->whereIn("line_users.user_id", $listFilter['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $listFilter));
            $filtered->each(function ($user) use ($line, $division_type, $result) {
                $result = $result->push($user?->allBelowDivisions($line)
                    ->where('division_type_id', '=', $division_type)->where('is_kol', 0));
            });
            $result = $result->collapse()->pluck('id')->unique()->toArray();
        }

        $fields = [];
        $accounts = collect([]);
        if (!$listFilter['action'] || $listFilter['action'] == 2 || $listFilter['action'] == 6) {
            $accounts = $this->active($listFilter, $result, $date);
            $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "classification", "acc_class", "account_type", "acc_mobile", "address", "ucode", "doctor", "doc_class", "doc_mobile", "speciality", "from_date", "to_date", "actions"];
        }
        if ($listFilter['action'] == 3) {
            $accounts = $this->inActive($listFilter, $result);
            $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "classification", "acc_class", "account_type", "acc_mobile", "address", "ucode", "doctor", "doc_class", "doc_mobile", "speciality", "from_date", "to_date", "actions"];
        }
        if ($listFilter['action'] == 4 || $listFilter['action'] == 5 || $listFilter['action'] == 1) {
            $accounts = $this->all($listFilter, $result);
            $fields = $this->getFields();
        }
        if ($listFilter['action'] == 6) {
            $accounts = $this->favPerAccount($listFilter, $result);
            $fields = ["s", "account_id", "line", "division", "brick", "account", "acc_code", "acc_class", "account_type", "speciality", "from_date", "to_date"];
        }
        if ($listFilter['action'] == 7) {
            $accounts = $this->masterList($listFilter, $result);
            $fields = ["account_id", "line", "division", "emp", "brick", "account", "acc_code", "classification", "acc_class", "account_type", "acc_mobile", "address", "ucode", "doctor", "speciality"];
        }
        if ($listFilter['action'] == 8) {
            $isAdmin = $authUser->hasRole('admin') || $authUser->hasRole('sub admin') || $authUser->hasRole('Gemstone Admin');
            $accounts = $this->verifiedLocation($listFilter, $result, $date, $isAdmin);
            $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "account_type", "verified", "from_date", "to_date", "location"];
        }

        LogActivity::addLog();
        return response()->json([
            "data" => [
                'accounts' => $accounts,
                'dates' => $dates,
                'fields' => $fields,
            ]
        ]);
    }
    public function getFields()
    {
        $settingFavourite = true;
        $setting = ListType::first()->type == 'Default List' ? true : false;
        if (!$setting) {
            $settingFavourite = ListType::first()->favourite_type == 'Doctor' ? true : false;
        }
        $fields = [];
        if ($setting) {
            $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "acc_class", "account_type", "acc_mobile", "address", "ucode", "doctor", "doc_class", "doc_mobile", "speciality", "from_date", "to_date", "actions"];
        } else {
            if ($settingFavourite) {
                $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "acc_class", "account_type", "acc_mobile", "address", "ucode", "doctor", "doc_class", "doc_mobile", "speciality", "from_date", "to_date", "actions"];
            } else {
                $fields = ["s", "account_id", "line", "division", "emp", "brick", "account", "acc_code", "acc_class", "account_type", "acc_mobile", "notes", "address", "from_date", "to_date", "actions"];
            }
        }
        return $fields;
    }
    public function favPerAccount($listFilter, $divisions)
    {
        return Account::select([
            'accounts.id as id',
            'accounts.id as account_id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'line_divisions.id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            DB::raw('IFNULL(crm_a.id,"") as class_id'),
            'account_types.name as account_type',
            'account_lines.id as acc_line_id',
            DB::raw('DATE_FORMAT(crm_account_lines.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_account_lines.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        ])
            ->join('account_types', function ($join) use ($listFilter) {
                $join->on('accounts.type_id', 'account_types.id')
                    ->whereIntegerInRaw('account_types.id', $listFilter['types']);
            })
            ->join('account_lines', function ($join) use ($listFilter, $divisions) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->where('account_lines.line_id', $listFilter['line'])
                    ->whereNull('account_lines.deleted_at')
                    ->where('account_lines.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                        ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                if (!empty($listFilter['brick_id'])) {
                    $join->whereIntegerInRaw('account_lines.brick_id', $listFilter['brick_id']);
                }
            })
            ->join(
                'new_account_doctors',
                function ($join) use ($listFilter) {
                    $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                    $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                        ->where('new_account_doctors.line_id', $listFilter['line'])
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where(fn($q) => $q->where('new_account_doctors.from_date', '<=', (string)Carbon::now())
                            ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
                }
            )
            ->join('doctors', function ($join) use ($listFilter) {
                $join->on('new_account_doctors.doctor_id', 'doctors.id')
                    ->whereIntegerInRaw('speciality_id', $listFilter['specialities']);
            })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)
                ->orWhere('accounts.inactive_date', '<=', (string)Carbon::now()))
            ->whereNull('accounts.deleted_at')
            ->orderBy('accounts.code', 'asc')->groupBy(
                "accounts.id",
                "lines.id",
                "line_divisions.id",
                "a.name",
                "account_lines.id",
                "account_lines.from_date",
                "account_lines.to_date",
            )->get();
    }
    public function resetAccountLocation(Request $request)
    {
        $account = $request->item;
        $account = AccountLines::where('account_id', $account['account_id'])
            ->where('line_id', $account['line_id'])
            ->where('line_division_id', $account['div_id'])
            ->where('brick_id', $account['brick_id'])
            ->update([
                'll' => null,
                'lg' => null,
            ]);
        // throw new CrmException($account);
        return $this->respondSuccess();
    }
    public function action(Request $request)
    {
        DB::transaction(function () use ($request) {
            match ($request->action) {
                2 => $this->setStatToAccounts($request->accounts),
                3 => $this->setStatToAccounts($request->accounts, false),
                4 => $this->deleteAccounts($request->accounts),
                5 => $this->resetLocations($request->accounts),
                6 => $this->resetMasterList($request->accounts),
                8 => $this->setVerifiedLocation($request->accounts),
            };
        });
        return $this->respondSuccess();
    }

    private function deleteAccounts($accounts)
    {
        $accountIds = collect($accounts)->pluck('account_id');
        $visitIds = ActualVisit::whereIntegerInRaw('account_id', $accountIds)->get()->pluck('id');
        VisitProduct::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        VisitGiveaway::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        VisitAttachment::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        ActualVisitManager::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        ActualDoubleFeedback::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        Statistic::whereIntegerInRaw('visit_id', $visitIds)->forceDelete();
        PlanVisitDetails::whereIntegerInRaw('visitable_id', $visitIds)->where('visitable_type', ActualVisit::class)->delete();
        $planIds = PlanVisit::whereIntegerInRaw('account_id', $accountIds)->get()->pluck('id');
        PlanVisitDetails::whereIntegerInRaw('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
        PlanVisit::whereIntegerInRaw('account_id', $accountIds)->forceDelete();
        AccountLines::whereIntegerInRaw('account_id', $accountIds)->update(['visit_id' => null]);
        AccountLines::whereIntegerInRaw('account_id', $accountIds)->forceDelete();
        ActualVisit::whereIntegerInRaw('account_id', $accountIds)->forceDelete();
        NewAccountDoctor::whereIntegerInRaw('account_id', $accountIds)->forceDelete();
        Account::whereIntegerInRaw('id', $accountIds)->forceDelete();
        foreach ($accountIds as $id) {
            LogActivity::addLog($id, Account::class, null, 'delete_accounts');
        }
    }
    private function setStatToAccounts($accounts, $active = true)
    {
        foreach ($accounts as $account) {
            NewAccountDoctor::where('account_id', $account['account_id'])->where('doctor_id', $account['doctor_id'])
                ->where('line_id', $account['line_id'])->update(['to_date' => $active ? Carbon::now()->addMonth(-1)->endOfMonth()->toDateString() : null]);
        }
    }

    private function resetLocations($accounts)
    {
        if (empty($accounts)) throw new Exception('Please Select Accounts Before Saving');
        $ids = [];
        $q = AccountLines::query();
        foreach ($accounts as $key => $account) {
            $baseWheres = [
                'account_id' => $account['account_id'],
                'line_id' => $account['line_id'],
                'line_division_id' => $account['div_id'],
            ];

            // Convert comma-separated brick_id string into an array
            $brickIds = explode(',', $account['brick_id']);

            // Flag to track if it's the first condition
            $isFirstCondition = ($key === 0);

            // Loop through each brick ID and add conditions
            foreach ($brickIds as $brickId) {
                $wheres = array_merge($baseWheres, ['brick_id' => $brickId]);

                if (!$isFirstCondition) { // Add as orWhere for subsequent conditions
                    $q->orWhere(function ($q) use ($wheres) {
                        $q->where($wheres);
                    });
                } else { // First condition uses where
                    $q->where($wheres);
                    $isFirstCondition = false; // Set flag to false after the first condition
                }
            }

            $ids[] = $account['account_id'];
        }
        // throw new CrmException($q->get());
        $q->update([
            'll' => null,
            'lg' => null,
            'visit_id' => null,
        ]);
        LogActivity::addLogs($ids, Account::class, null, 'reset_account_locations');
    }
    private function resetMasterList($accounts)
    {
        foreach ($accounts as $account) {
            $accountLine = AccountLines::where('account_id', $account['account_id'])
                ->where('line_id', $account['line_id'])
                ->where('line_division_id', $account['div_id'])
                ->where('brick_id', $account['brick_id'])
                ->where('account_lines.from_date', '<=', (string)Carbon::now())
                ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                    ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
                ->first();
            if ($accountLine) {
                $checkAccountLines = AccountLines::where('account_id', $accountLine->account_id)
                    ->where('line_id', $accountLine->line_id)
                    ->where('line_division_id', $accountLine->line_division_id)
                    ->where('brick_id', $accountLine->brick_id)->get();
                $checkAccountLinesCount = $checkAccountLines->count();
                if ($checkAccountLinesCount == 1) {
                    $accountLine->update([
                        'from_date' => '2035-01-01',
                        'to_date' => null,
                    ]);
                    NewAccountDoctor::where('account_id', $accountLine->account_id)
                        ->where('account_lines_id', $accountLine->id)->where('line_id', $accountLine->line_id)->update([
                            'from_date' => '2035-01-01',
                            'to_date' => null,
                        ]);
                }
                if ($checkAccountLinesCount > 1) {
                    $accountLine->delete();
                    NewAccountDoctor::where('account_id', $accountLine->account_id)
                        ->where('account_lines_id', $accountLine->id)->where('line_id', $accountLine->line_id)->delete();
                }
            }
        }
    }
    private function setVerifiedLocation($accounts)
    {
        /**@var User $authUser */
        $authUser = Auth::user();
        $isAdmin = $authUser->hasRole('admin') || $authUser->hasRole('sub admin') || $authUser->hasRole('Gemstone Admin');
        if ($isAdmin) {
            AccountLocationVerification::whereIntegerInRaw('account_lines_id', collect($accounts)->pluck('account_line_id'))->delete();
        }
        foreach ($accounts as $account) {
            AccountLocationVerification::firstOrCreate([
                'account_lines_id' => $account['account_line_id'],
                'user_id' => $authUser->id,
            ], [
                'verified' => 1,
            ]);
        }
    }
}
