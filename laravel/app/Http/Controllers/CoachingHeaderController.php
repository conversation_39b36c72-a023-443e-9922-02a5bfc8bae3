<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\ActualVisitSetting;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\CoachingHeaderRequest;
use App\Models\Coaching\CoachingHeader;
use App\Line;
use App\LineDivision;
use App\LineDivisionType;
use App\Models\Coaching\Answer;
use App\Models\Coaching\Category;
use App\Models\Coaching\CoachingCheckedCategory;
use App\Models\Coaching\CoachingDetail;
use App\Models\Coaching\Type;
use App\Models\CoachingSetting;
use App\Notifications\CoachingCreatedNotification;
use App\StartPlanDay;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CoachingHeaderController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CoachingHeaderRequest $request)
    {
        /**@var User $user */
        $user = Auth::user();
        DB::transaction(function () use ($request, $user) {
            $header = CoachingHeader::create([
                'evaluator_id' => $request->evaluator_id,
                'employee_id' => $request->employee_id,
                'date' => $request->date,
                'strenghts' => $request->strenghts,
                'points_of_improvement' => $request->points_of_improvement,
                'notes' => $request->notes,
            ]);
            foreach ($request->results as $result) {
                foreach ($result['questions'] as $question) {
                    // throw new CrmException($question['checked']);
                    if ($question['checked']) {
                        CoachingCheckedCategory::firstOrCreate([
                            'coaching_header_id' => $header->id,
                            'category_id' => $result['category_id'],
                            'question_id' => $question['question_id'],
                            'checked' => $question['checked'],
                            'date' => $request->date,
                        ]);
                    }
                    $detail = new CoachingDetail();
                    $detail->coaching_header_id = $header->id;
                    $detail->category_id = $result['category_id'];
                    $detail->question_id = $question['question_id'];
                    $detail->answer_id = $question['answer_id'];
                    $detail->reason = $question['reason'];
                    $detail->save();
                }
            }
        });

        NotificationHelper::send(
            collect($user->approvableUsers($request->line_id)),
            new CoachingCreatedNotification('Coaching Created', auth()->user())
        );
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Coaching\CoachingHeader  $coachingHeader
     * @return \Illuminate\Http\Response
     */
    public function show(CoachingHeader $coachingHeader)
    {
        //
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Coaching\CoachingHeader  $coachingHeader
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CoachingHeader $coachingHeader)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Coaching\CoachingHeader  $coachingHeader
     * @return \Illuminate\Http\Response
     */
    public function destroy(CoachingHeader $coachingHeader)
    {
        $model_id = $coachingHeader->id;
        $model_type = CoachingHeader::class;
        if (count($coachingHeader->details) > 0)
            $coachingHeader->details()->delete();
        $coachingHeader->delete();
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }

    public function getLines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        return $this->respond($lines);
    }

    public function getBelowUsers(User $user, Line $line)
    {
        // if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
        //     $users = $line->users()->wherePivot('deleted_at', null)
        //         ->where('menuroles', 'not like', "%admin%")
        //         ->get();
        // } else {
        //     if($user->hasPosition()){
        //         $users = 
        //     }else{
        //         $users = $user->allBelowUsers($line);
        //     }
        // }
        $users = $user->belowUsersWithPositions($line)->filter(
            fn($collectionUser)
            => $collectionUser != null
            // &&
            // $collectionUser->id !== $user->id
        );
        return $this->respond($users->values());
    }

    // public function getLineData(Line $line)
    // {
    //     /**@var User $user */
    //     $user = Auth::user();
    //     $users = collect([]);
    //     $division_type = DivisionType::where('last_level', '=', 1)->first();
    //     $divisions = $user->userDivisions($line)->where('division_type_id', $division_type->id);;
    //     $users = $user->belowUsersWithPositions($line);
    //     $typesWithoutLines = $typesWithoutLines = Type::whereNull('line_id')->get();
    //     $types = collect([]);
    //     if (!empty($typesWithoutLines)) {
    //         $types = Type::get();
    //     } else {
    //         $types = Type::where('line_id', $line->id)->get();
    //     }
    //     return response()->json([
    //         'divisions' => $divisions->values(),
    //         'users' => $users,
    //         'types' => $types,
    //         'line' => $line->name,
    //     ]);
    // }


    public function performanceSummaryReport(Request $request)
    {
        $request->validate([
            'from_date'     => 'date',
            'to_date'  => 'date|after_or_equal:from_date',
        ]);

        $headers = null;
        /**@var User $user */
        $user = Auth::user();
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $headers = CoachingHeader::whereNull('deleted_at')
                ->where('employee_id', $request->employee_id)
                ->whereBetween('date', [
                    $request->from_date . ' 00:00:00',
                    $request->to_date . ' 23:59:59'
                ])
                ->get();
        } else {

            $headers = CoachingHeader::where('employee_id', $request->employee_id)
                ->whereBetween('date', [
                    $request->from_date . ' 00:00:00',
                    $request->to_date . ' 23:59:59'
                ])
                ->get();
        }

        $headersData = $headers->map(function ($header) {
            return [
                'id' => $header->id,
                'evaluator' => $header->evaluator->fullname,
                'employee' => $header->employee->fullname,
                'line' => $header->employee->lines->first()->name,
                'date' => $header->date,
                'status' => isNullable($header->approval) ? 'Not Applied' : 'Approved',
                'area_of_improvement' => $header->points_of_improvement,
                'strenghts' => $header->strenghts,
                'notes' => $header->notes,
                'details' => $header->details
                    ->groupBy(fn($item) =>  $item->category->id)->values()
                    ->map(function ($item) {
                        return [
                            'category' => $item[0]->category->name,
                            'category_score' => $item->reduce(function ($score, $subItem) {
                                return $score += $subItem->answer->weight;
                            }, 0),
                            'questions' => $item->map(function ($subItem) {
                                return [
                                    'question' => $subItem->question->name,
                                    'answer' => $subItem->answer->name,
                                    'answer_weight' => $subItem->answer->weight,
                                    'reason' => $subItem->reason ?? '',
                                ];
                            }),
                        ];
                    }),
            ];
        });



        return response()->json([
            'headersData' => $headersData,
        ]);
    }

    public function statisticsReport(Request $request)
    {
        $request->validate([
            'from_date'     => 'date',
            'to_date'  => 'date|after_or_equal:from_date',
        ]);

        $from = Carbon::parse($request['from_date'])->startOfDay();
        $to = Carbon::parse($request['to_date'])->endOfDay();
        // filtered employeeIds
        $employeeIds = $request->users;

        // get categories according to filtered types
        $typesWithoutLines = Type::whereNull('line_id')->get();
        $categoryIds = [];
        if (!empty($typesWithoutLines)) {
            $categoryIds = Type::when(!empty($request->type_ids), fn($q) => $q->whereIn('id', $request->type_ids))
                ->get()->map(fn($type) => $type->categories)->flatten()->pluck('id');
        } else {
            $categoryIds = Type::where('line_id', $request->line_id)
                ->when(!empty($request->type_ids), fn($q) => $q->whereIn('id', $request->type_ids))
                ->get()->map(fn($type) => $type->categories)->flatten()->pluck('id');
        }

        // evaluator logged in user
        /**@var USer */
        $authUser = Auth::user();

        // line according to filtered line
        $lines = Line::whereIntegerInRaw('id', $request->line_id)->get();

        // fitlered categories
        $categories = Category::whereIn('id', $categoryIds)->get();

        $fields = collect([
            'id',
            'line',
            'division',
            'employee',
            'evaluator',
            'insertion_date',
            'coaching_date',
            'visits',
            'coaching_count',
            'status',
            'questions_count',
            ...$categories->pluck('name'),
            'degree',
            'total',
            'ratio',
        ]);

        $headers = collect([]);
        $filtered = new Collection([]);

        foreach ($lines as $line) {
            $users = $line->users($from, $to)->when(!empty($employeeIds), fn($q) => $q->whereIn("line_users.user_id", $employeeIds))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $request));
        }

        // employees when divisionIds are selected
        // if ($divisionIds) {
        //     $divisions = $line->divisions($from, $to)
        //         ->when(!empty($divisionIds), fn($q) => $q->whereIn("line_divisions.id", $divisionIds))->get();

        //     $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $request));
        //     $filtered = $filtered->map(function ($division) use ($from, $to) {
        //         $user = $division->user($from, $to);
        //         return $user;
        //     });
        // }

        $filtered->each(function ($object) use ($from, $to, $headers, $categories) {
            $headers = $headers->push($this->coachingPerUser($headers, $object, $categories, $from, $to));
        });

        return response()->json([
            'results' => $headers->unique("id")->values(),
            'fields' => $fields,
        ]);
    }

    public function coachingPerUser($results, $user, $categories, $from, $to)
    {
        $lines = $user->lines($from, $to)->get();
        $lineIds = $lines->pluck('id')->toArray();
        $results = collect([
            'id' => $user->id,
            'line' => $lines->pluck('name')->implode(','),
            'division' => $user->divisions($from, $to)->where('is_kol', 0)->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
            'employee' => $user->fullname,
            'color' => $user->divisions($from, $to)->where('is_kol', 0)->first()?->DivisionType->color,
        ]);
        $coachingHeaders = CoachingHeader::where('employee_id', $user->id)
            ->whereBetween('date', [$from, $to]);
        $coachingDates = [];
        $degree = 0;
        $countHeaders = 0;
        $countDetails = 0;

        $maxWeightAnswers = 0;
        $status = collect([]);
        $categories->each(function ($category) use (&$coachingDates, &$status, $coachingHeaders, $results, &$degree, &$countHeaders, &$countDetails, &$maxWeightAnswers) {
            $catAvg = 0;
            $catTotal = 0;
            $max = 0;

            // get coaching headers
            $coachingHeaders = $coachingHeaders->with(['details' => function ($query) use ($category) {
                $query->where('category_id', $category->id);
            }])->get();
            foreach ($coachingHeaders as $coachingHeader) {
                $status = $status->push(isNullable($coachingHeader->approval) ? 'Not Applied' : 'Approved');
            }
            $status = $status->unique()->values();
            $countHeaders = $coachingHeaders->count();
            // $status = $coachingHeaders->pluck('approval');
            $coachingDates = $coachingHeaders->map(fn($header) => Carbon::parse($header->date)->toDateString());
            $results->put('evaluator', $coachingHeaders->map(fn($header) => $header->evaluator->fullname)->implode(', ') ?? '');
            $results->put('coaching_date', $coachingDates->implode(', '));
            $results->put('insertion_date', $coachingHeaders->map(fn($header) => Carbon::parse($header->created_at)->toDateString())->implode(', '));
            // coaching number per user
            $counter = $coachingHeaders->filter(fn($header) => !$header->details->isEmpty())->count();

            // get category total per user
            $headers = $coachingHeaders->pluck('details')->collapse()
                ->each(function ($detail) use (&$catTotal, &$max) {
                    $max += $detail->question->answers()->max('weight');
                    $catTotal += Answer::find($detail->answer_id)->weight;
                });
            $countDetails += $headers->count();

            // average per category for each user
            $catAvg = $counter != 0 ? round(($catTotal / $counter), 2) : 0;
            $maxWeightAnswers += $counter != 0 ? round(($max / $counter), 2) : 0;

            // total averages per category for each user
            $degree += $catAvg;

            $results->put($category->name, $catAvg);
        });

        // throw new CrmException($coachingDates);
        $results->put('coaching_count', $countHeaders);
        $results->put('visits', ActualVisit::WhereIn(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d'))"), $coachingDates)->where('user_id', $user->id)->count());

        $results->put('status', $status->implode(','));
        $results->put('questions_count', $countDetails);
        $results->put('degree', $degree);
        $results->put('total', $maxWeightAnswers);
        $results->put('ratio', $maxWeightAnswers ? round(($degree / $maxWeightAnswers) * 100, 2) . '%' : 0);

        return $results;
    }

    public function sortData($data, $line)
    {

        $typesCount = LineDivisionType::where('line_id', $line->id)->get()->filter(function ($item) {
            return $item->active;
        })->count() - 1;
        $types = "";
        foreach (range(1, $typesCount) as $value) {
            if ($value == $typesCount) {
                $types .= 'divisionChildren';
            } else {
                $types .= 'divisionChildren.';
            }
        }
        $divisions = $this->flatten(LineDivision::whereTypeRoot($line)->with($types)->get());

        $sorted = $divisions->map(function ($division) use ($data) {
            return $data->where('division', $division['name'])->first();
        })->filter(fn($item) => $item != null);
        return $sorted;
    }

    public function flatten(Collection $items)
    {
        $divisions = collect([]);

        foreach ($items as $country) {
            $divisions->push(['id' => $country->id, 'name' => $country->name]);
            foreach ($country->divisionChildren as $area) {
                $divisions->push(['id' => $area->id, 'name' => $area->name]);
                foreach ($area->divisionChildren as $district) {
                    $divisions->push(['id' => $district->id, 'name' => $district->name]);
                    foreach ($district->divisionChildren as $territory) {
                        $divisions->push(['id' => $territory->id, 'name' => $territory->name]);
                    }
                }
            }
        }
        return $divisions;
    }

    public function change_approval(Request $request)
    {
        $coachingHeader = CoachingHeader::find($request->id);
        $coachingHeader->approval = 1;
        $coachingHeader->save();
        return $this->respondSuccess();
        //    throw new CrmException($coachingHeader);
    }

    public function getDates()
    {
        $coachingTime = CoachingSetting::where('key', 'coaching_time_work_with_visit_time')->value('value') == 'Yes';
        $min_line_actual_visit_date = Carbon::now();
        if ($coachingTime) {
            $actual_start_day_value = ActualVisitSetting::where('key', 'actual_start_day')->value('value');
            $actual_start_day = StartPlanDay::where('name', $actual_start_day_value)->first();
            $min_line_actual_visit_date = Carbon::today()->addDays($actual_start_day->day)->toDateString();
        }
        return $this->respond($min_line_actual_visit_date);
        // return $this->respond(Carbon::now()->toDateString());

    }
}
