<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {


        // Sales Details Service view for detailed reporting
        $this->createSalesDetailsView();
    }

    private function createSalesDetailsView(): void
    {
        $product_avg_price = $this->getProductAvgPriceExpression();
        $employeeSubquery = $this->getEmployeeSubquery();
        $empCodeSubquery = $this->getEmpCodeSubquery();

        $query = DB::table('sales')
            ->selectRaw('DISTINCT crm_sales_details.id as detail_id')
            ->addSelect([
                'sales.id as id',
                'lines.name as line',
                'line_divisions.name as division',
                'sales_details.div_id as div_id'
            ])
            ->selectRaw('IFNULL(crm_bricks.name,"") AS brick')
            ->selectRaw('IFNULL(crm_sales_details.brick_id,"") AS brick_id')
            ->selectRaw('DATE_FORMAT(crm_sales_details.date,"%Y-%m-%d") as date')
            ->selectRaw('IFNULL(crm_higher.fullname,"") AS manager')
            ->selectRaw("($employeeSubquery) as employee")
            ->selectRaw("($empCodeSubquery) as emp_code")
            ->selectRaw('IFNULL(crm_distributors.name,"") AS distributor')
            ->addSelect([
                'mappings.distributor_id',
                'sales_types.name as type',
                'mappings.name as name',
                'mappings.id as mapping_id',
                'mappings.code as mapping_code'
            ])
            ->selectRaw("
                CASE
                    WHEN crm_sales.ceiling = '0' THEN 'NORMAL'
                    WHEN crm_sales.ceiling = '2' THEN 'DISTRIBUTED'
                END as distribution_type
            ")
            ->selectRaw('IFNULL(crm_mappings.address,"") AS address')
            ->addSelect([
                'sales.product_id',
                'products.name as product',
                'division_types.color as color'
            ])
            ->selectRaw('FORMAT((crm_sales_details.quantity),2) as units')
            ->selectRaw("FORMAT(($product_avg_price),2) as pro_price")
            ->addSelect('sales_details.bonus as bonus')
            ->selectRaw("
                CASE
                    WHEN crm_sales.value = 0 THEN FORMAT(($product_avg_price * crm_sales_details.quantity),2)
                    ELSE FORMAT(crm_sales_details.value,2)
                END as value
            ")
            ->addSelect('sales_details.ratio')
            ->join('products', 'sales.product_id', '=', 'products.id')
            ->join('sales_details', 'sales.id', '=', 'sales_details.sale_id')
            ->leftJoin('mapping_sale', 'sales.id', '=', 'mapping_sale.sale_id')
            ->leftJoin('target_details', function ($join) {
                $join->on('sales.product_id', '=', 'target_details.product_id')
                    ->whereColumn('sales_details.div_id', 'target_details.div_id')
                    ->whereColumn('sales_details.brick_id', 'target_details.brick_id')
                    ->whereColumn('sales_details.date', 'target_details.date');
            })
            ->join('mappings', 'mapping_sale.mapping_id', '=', 'mappings.id')
            ->leftJoin('distributors', 'sales.distributor_id', '=', 'distributors.id')
            ->leftJoin('sales_types', 'mappings.mapping_type_id', '=', 'sales_types.id')
            ->leftJoin('bricks', 'sales_details.brick_id', '=', 'bricks.id')
            ->leftJoin('line_divisions', 'sales_details.div_id', '=', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', '=', 'division_types.id')
            ->leftJoin('line_users_divisions as low_level', function ($join) {
                $join->on('line_divisions.id', '=', 'low_level.line_division_id')
                    ->whereNull('low_level.deleted_at');
            })
            ->leftJoin('users as mr', 'low_level.user_id', '=', 'mr.id')
            ->leftJoin('line_div_parents', function ($join) {
                $join->on('line_divisions.id', '=', 'line_div_parents.line_div_id')
                    ->whereNull('line_div_parents.deleted_at');
            })
            ->leftJoin('line_divisions as parent', 'line_div_parents.parent_id', '=', 'parent.id')
            ->leftJoin('line_users_divisions as high_level', 'line_div_parents.parent_id', '=', 'high_level.line_division_id')
            ->leftJoin('users as higher', 'high_level.user_id', '=', 'higher.id')
            ->leftJoin('lines', 'line_divisions.line_id', '=', 'lines.id')
            ->whereIn('sales.ceiling', ['0', '2']);

        $sql = $query->toRawSql();
        DB::statement("CREATE OR REPLACE VIEW sales_view AS $sql");
    }

    private function getProductAvgPriceExpression(): string
    {
        return '
            COALESCE(
                (
                    SELECT avg_price FROM crm_product_prices
                    WHERE crm_product_prices.product_id = crm_sales.product_id
                    AND crm_product_prices.deleted_at IS NULL
                    AND crm_product_prices.distributor_id = crm_sales.distributor_id
                    AND crm_product_prices.from_date <= crm_sales_details.date
                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                    ORDER BY crm_product_prices.from_date DESC
                    LIMIT 1
                ),
                (
                    SELECT avg_price FROM crm_product_prices
                    WHERE crm_product_prices.product_id = crm_sales.product_id
                    AND crm_product_prices.deleted_at IS NULL
                    AND crm_product_prices.distributor_id IS NULL
                    AND crm_product_prices.from_date <= crm_sales_details.date
                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                    ORDER BY crm_product_prices.from_date DESC
                    LIMIT 1
                )
        )';
    }

    private function getEmployeeSubquery(): string
    {
        return '
            SELECT IFNULL(crm_users.fullname,"")
            FROM crm_users
            JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
            WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
            AND crm_line_users_divisions.deleted_at IS NULL
            AND crm_users.deleted_at IS NULL
            AND crm_line_users_divisions.from_date <= crm_sales_details.date
            AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
            LIMIT 1
        ';
    }

    private function getEmpCodeSubquery(): string
    {
        return '
            SELECT IFNULL(crm_users.emp_code,"")
            FROM crm_users
            JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
            WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
            AND crm_line_users_divisions.deleted_at IS NULL
            AND crm_users.deleted_at IS NULL
            AND crm_line_users_divisions.from_date <= crm_sales_details.date
            AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
            LIMIT 1
        ';
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP VIEW IF EXISTS sales_view');
    }
}
;
