<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
//        Schema::table('new_account_doctors', function (Blueprint $table) {
//            DB::statement('
//                UPDATE crm_new_account_doctors
//                JOIN crm_doctors ON crm_new_account_doctors.doctor_id = crm_doctors.id
//                SET crm_new_account_doctors.class_id = crm_doctors.class_id
//            ');
//        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
//        Schema::table('new_account_doctors', function (Blueprint $table) {
//            // Optional: Set class_id to NULL if you want to reverse
//            DB::statement('
//                UPDATE crm_new_account_doctors
//                SET class_id = NULL
//            ');
//        });
    }
};
