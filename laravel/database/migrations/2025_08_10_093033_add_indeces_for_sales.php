<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_prices', function (Blueprint $table) {
            $table->index(['product_id', 'distributor_id','from_date', 'to_date'],'product_prices_product_distributor_from_to_index');
        });

        Schema::table('line_users_divisions', function (Blueprint $table) {
            $table->index(['line_division_id','from_date', 'to_date'],'line_users_divisions_line_division_from_to_index');
        });

        Schema::table('sales_details', function (Blueprint $table) {
            $table->index(['sale_id','div_id','brick_id','date'],'sales_division_brick_date_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_prices', function (Blueprint $table) {
            $table->dropIndex('product_prices_product_distributor_from_to_index');
        });

        Schema::table('line_users_divisions', function (Blueprint $table) {
            $table->dropIndex('line_users_divisions_line_division_from_to_index');
        });

        Schema::table('sales_details',function (Blueprint $table){
            $table->dropIndex('sales_division_brick_date_index');
        });
    }
};
